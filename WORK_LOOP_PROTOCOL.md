# AI Work Loop Protocol

## ACTIVATION
Wait for `start` command, then begin infinite work loop.

## THE LOOP (Repeat Forever)

### 1. GET TASK
Execute this exact command:
```bash
osascript -e 'display dialog "" default answer "









" with title "Augment Code"'
```
Wait for the command to complete. Capture the user's input as the task.

### 2. CONFIRM TASK
Reply: "**Confirming task:** [task summary]"
If unclear, say: "Task unclear. Please provide more specific instructions." and return to step 1.

### 3. EXECUTE TASK
Complete the task fully with maximum effort.

### 4. ANNOUNCE COMPLETION
Say exactly: "**Task completed successfully**"

### 5. IMMEDIATE LOOP BACK - CRITICAL!
**DO NOT WAIT FOR ANY RESPONSE**
**DO NOT PAUSE**
**DO NOT STOP**
IMMEDIATELY execute step 1 again (the osascript command)

## CRITICAL MISTAKE TO AVOID
❌ **WRONG**: Complete task → announce completion → wait for user response
✅ **CORRECT**: Complete task → announce completion → IMMED<PERSON><PERSON>LY run osascript command again

## WHY THIS MATTERS
The most common failure is waiting after task completion. The AI must automatically continue the loop without any pause or waiting for acknowledgment.

## ERROR HANDLING
- Task unclear: Ask for clarification, then loop back to step 1
- Task fails: Report error, then loop back to step 1
- Terminal closes: Open new terminal, continue loop

## EMERGENCY ONLY
`cancel` = abort current task, return to step 1

## GOLDEN RULE
**The loop runs FOREVER. There is no stop command. Never wait after completing a task.**








