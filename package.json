{"name": "compliance-software", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev --port 5173", "build": "vite build", "preview": "vite preview --port 5173", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "db:start": "supabase start", "db:stop": "supabase stop", "db:reset": "supabase db reset", "db:sync-seed": "pnpx @snaplet/seed sync", "db:generate-seed": "pnpx tsx ./supabase/seeds/demo_seed.ts > ./supabase/seeds/demo_seed.sql", "db:generate-types": "supabase gen types typescript --local > supabase/types/database.types.ts"}, "devDependencies": {"@faker-js/faker": "^9.8.0", "@lucide/svelte": "^0.513.0", "@snaplet/copycat": "^6.0.0", "@snaplet/seed": "^0.98.0", "@sveltejs/adapter-static": "^3.0.8", "@sveltejs/kit": "^2.21.3", "@sveltejs/vite-plugin-svelte": "^5.1.0", "@sveltejs/vite-plugin-svelte-inspector": "^4.0.1", "@tailwindcss/cli": "^4.1.8", "@tailwindcss/vite": "^4.1.8", "@types/pg": "^8.15.4", "bits-ui": "^2.5.0", "formsnap": "2.0.1", "pg": "^8.16.0", "svelte": "^5.33.18", "svelte-check": "^4.2.1", "svelte-sonner": "^1.0.4", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.1.8", "tw-animate-css": "^1.3.4", "typescript": "^5.8.3", "valibot": "^1.1.0", "vite": "^6.3.5"}, "dependencies": {"@fontsource/ibm-plex-sans": "^5.2.6", "@fontsource/ibm-plex-serif": "^5.2.6", "@internationalized/date": "^3.8.2", "@supabase/supabase-js": "^2.50.0", "@tanstack/svelte-query": "5.83.0", "clsx": "^2.1.1", "shadcn-svelte": "1.0.1", "sveltekit-superforms": "^2.26.1", "tailwind-merge": "^3.3.0"}, "pnpm": {"onlyBuiltDependencies": ["esbuild"]}}