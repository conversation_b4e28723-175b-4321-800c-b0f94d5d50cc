<script lang="ts">
    import {SearchX, X} from '@lucide/svelte';
    import type {Component} from 'svelte';
    import EmptyState from './EmptyState.svelte';

    interface Props {
        title?: string;
        description?: string;
        icon?: Component;
        onClear: () => void;
    }

    let {
        title = 'Nothing found',
        description = 'No match for your search criteria.',
        icon = SearchX,
        onClear
    }: Props = $props();
</script>

<EmptyState
    {icon}
    {title}
    {description}
    action={{
        label: 'Clear search',
        icon: X,
        onclick: onClear
    }}
/>
