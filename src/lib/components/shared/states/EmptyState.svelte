<script lang="ts">
    import type {Component} from 'svelte';
    import BaseState from './BaseState.svelte';

    interface ActionButton {
        label: string;
        icon?: Component;
        onclick: () => void;
    }

    interface Props {
        icon?: Component;
        title: string;
        description: string;
        action?: ActionButton | null;
    }

    let {
        icon,
        title,
        description,
        action = null,
    }: Props = $props();
</script>

<BaseState
        {icon}
        iconColor="muted"
        {title}
        {description}
        {action}
/>
