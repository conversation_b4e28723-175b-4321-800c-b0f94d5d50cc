<script lang="ts">
    import type {Component} from 'svelte';
    // @ts-ignore
    import * as Button from '$lib/components/ui/button';

    interface ActionButton {
        label: string;
        icon?: Component;
        onclick: () => void;
    }

    interface Props {
        icon?: Component;
        iconColor?: 'destructive' | 'muted';
        title: string;
        description: string;
        action?: ActionButton | null;
    }

    let {
        icon,
        iconColor = 'muted',
        title,
        description,
        action = null,
    }: Props = $props();
</script>

<div class="col-span-full">
    <div class="flex flex-col items-center justify-center py-16 px-4 text-center">
        <div class="relative max-w-md mx-auto">
            <div class="absolute -inset-16 rounded-full blur-3xl bg-background/80"></div>

            <div class="relative z-10 text-center">
                <div class="mb-6 flex justify-center">
                    {#if icon}
                        {@const IconComponent = icon}
                        <IconComponent class="size-12 {iconColor === 'destructive' ? 'text-destructive' : 'text-muted-foreground'}"/>
                    {/if}
                </div>

                <h3 class="text-xl font-semibold text-foreground mb-2">
                    {title}
                </h3>

                <p class="text-muted-foreground mb-8 max-w-md leading-relaxed">
                    {description}
                </p>

                {#if action}
                    <Button.Root variant="outline" onclick={action.onclick}>
                        {#if action.icon}
                            {@const ActionIcon = action.icon}
                            <ActionIcon class="size-4"/>
                        {/if}
                        {action.label}
                    </Button.Root>
                {/if}
            </div>
        </div>
    </div>
</div>
