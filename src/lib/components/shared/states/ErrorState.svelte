<script lang="ts">
    import {AlertTriangle, RefreshCw} from '@lucide/svelte';
    import BaseState from './BaseState.svelte';

    interface Props {
        error: string;
        onRetry?: () => void;
    }

    let {
        error,
        onRetry,
    }: Props = $props();

    // Create action for retry if provided
    const retryAction = onRetry ? {
        label: 'Try again',
        icon: RefreshCw,
        onclick: onRetry,
    } : null;
</script>

<BaseState
        icon={AlertTriangle}
        iconColor="destructive"
        title="Something went wrong"
        description={error}
        action={retryAction}
/>
