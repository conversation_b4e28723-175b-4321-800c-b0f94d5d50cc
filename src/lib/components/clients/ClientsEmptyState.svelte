<script lang="ts">
    import {UserX, Plus} from '@lucide/svelte';
    import EmptyState from '$lib/components/shared/states/EmptyState.svelte';

    interface Props {
        onInvite: () => void;
    }

    let {onInvite}: Props = $props();
</script>

<EmptyState
        icon={UserX}
        title="No clients yet"
        description="Start by inviting your first client."
        action={{
        label: 'Invite Client',
        icon: Plus,
        onclick: onInvite
    }}
/>
