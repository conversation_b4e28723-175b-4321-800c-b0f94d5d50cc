<script lang="ts">
    import {Skeleton} from '$lib/components/ui/skeleton';
    // @ts-ignore
    import * as Button from '$lib/components/ui/button';

    interface Props {
        count?: number;
    }

    let {count = 12}: Props = $props();

    // Determine visibility class based on card index for responsive 3-row layout
    const getVisibilityClass = (i: number) =>
        i < 3 ? '' :                        // Cards 1-3: Always visible (mobile: 1×3 = 3 cards)
            i < 6 ? 'hidden sm:block' :     // Cards 4-6: Tablet+ (2×3 = 6 cards total)
                i < 9 ? 'hidden xl:block' : // Cards 7-9: Desktop+ (3×3 = 9 cards total)
                    'hidden 2xl:block';     // Cards 10-12: Large+ (4×3 = 12 cards total)
</script>

<div class="contents relative">
    {#each Array(count) as _, index (index)}
        <Button.Root
                variant="outline"
                class="group h-auto p-0 w-full {getVisibilityClass(index)}"
                disabled
        >
            <div class="p-4 w-full flex flex-col text-left space-y-3">
                <div class="flex items-start gap-3">
                    <Skeleton class="size-11 rounded-lg flex-shrink-0"/>
                    <div class="flex-1 min-w-0 space-y-1">
                        <Skeleton class="h-[20px] w-3/4"/>
                        <div class="h-[20px] flex items-center">
                            <Skeleton class="w-3 h-3 mr-1 flex-shrink-0"/>
                            <Skeleton class="h-[16px] w-1/2"/>
                        </div>
                    </div>
                </div>
                <div class="h-6 flex items-center">
                    <Skeleton class="h-[16px] w-16"/>
                    <Skeleton class="ml-1 size-3"/>
                </div>
            </div>
        </Button.Root>
    {/each}
</div>

<!-- Fade overlay covering the skeleton cards area -->
<div class="mt-20 col-span-full pointer-events-none absolute inset-0 bg-gradient-to-t from-background to-transparent z-10"></div>
