<script lang="ts">
    import { RefreshCw, SortAsc, SortDesc, Plus } from '@lucide/svelte';
    // @ts-ignore
    import * as Button from '$lib/components/ui/button';
    // @ts-ignore
    import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
    import type { ClientsFilters } from '$lib/stores/clientsStore';

    interface Props {
        isLoading?: boolean;
        clientsCount?: number;
        filters: ClientsFilters;
        onRefresh?: () => void;
        onSortChange?: (sortBy: ClientsFilters['sortBy'], sortOrder: ClientsFilters['sortOrder']) => void;
        onAddClient?: () => void;
    }

    let {
        isLoading = false,
        clientsCount = 0,
        filters,
        onRefresh,
        onSortChange,
        onAddClient,
    }: Props = $props();

    const sortOptions = [
        { value: 'name', label: 'Name' },
        { value: 'city', label: 'City' },
        { value: 'country', label: 'Country' },
        { value: 'created_at', label: 'Date Added' },
    ] as const;

    function handleSortChange(sortBy: ClientsFilters['sortBy']) {
        const newOrder = filters.sortBy === sortBy && filters.sortOrder === 'asc' ? 'desc' : 'asc';
        onSortChange?.(sortBy, newOrder);
    }

    let currentSortLabel = $derived.by(() => {
        const option = sortOptions.find(opt => opt.value === filters.sortBy);
        return option?.label || 'Name';
    });
</script>

<div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
    <div class="flex items-center gap-2 text-sm text-muted-foreground">
        <span>
            {clientsCount} {clientsCount === 1 ? 'client' : 'clients'}
        </span>
    </div>

    <div class="flex items-center gap-2">
        <!-- Refresh Button -->
        <Button.Root
            variant="outline"
            onclick={onRefresh}
            disabled={isLoading}
        >
            <RefreshCw class="h-4 w-4 {isLoading ? 'animate-spin' : ''}" />
            <span class="ml-2">Refresh</span>
        </Button.Root>

        <!-- Sort Dropdown -->
        <DropdownMenu.Root>
            <DropdownMenu.Trigger>
                {#snippet child({props})}
                    <Button.Root
                        variant="outline"
                        {...props}
                    >
                        {#if filters.sortOrder === 'asc'}
                            <SortAsc class="h-4 w-4" />
                        {:else}
                            <SortDesc class="h-4 w-4" />
                        {/if}
                        <span class="ml-2">{currentSortLabel}</span>
                    </Button.Root>
                {/snippet}
            </DropdownMenu.Trigger>

            <DropdownMenu.Content align="end" class="w-40">

                {#each sortOptions as option}
                    <DropdownMenu.Item
                        onclick={() => handleSortChange(option.value)}
                        class="flex items-center justify-between cursor-pointer"
                    >
                        <span class="font-medium">{option.label}</span>
                        {#if filters.sortBy === option.value}
                            {#if filters.sortOrder === 'asc'}
                                <SortAsc class="h-4 w-4 text-muted-foreground" />
                            {:else}
                                <SortDesc class="h-4 w-4 text-muted-foreground" />
                            {/if}
                        {/if}
                    </DropdownMenu.Item>
                {/each}
            </DropdownMenu.Content>
        </DropdownMenu.Root>

        <!-- Invite Client Button -->
        {#if onAddClient}
            <Button.Root onclick={onAddClient}>
                <Plus class="h-4 w-4" />
                <span class="ml-2">Invite Client</span>
            </Button.Root>
        {/if}
    </div>
</div>
