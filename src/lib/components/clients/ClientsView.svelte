<script lang="ts">
    import {toast} from 'svelte-sonner';
    import ClientsSearchBar from './ClientsSearchBar.svelte';
    import ClientsToolbar from './ClientsToolbar.svelte';
    import ClientsLoadingState from './ClientsLoadingState.svelte';
    import ClientsGrid from './ClientsGrid.svelte';
    import SearchEmptyState from '$lib/components/shared/states/SearchEmptyState.svelte';
    import ClientsEmptyState from './ClientsEmptyState.svelte';
    import ErrorState from '$lib/components/shared/states/ErrorState.svelte';

    import {
        createClientsQuery,
        filtersStore,
        queryFiltersStore,
        clientsActions,
        type ClientsFilters,
    } from '$lib/stores/clientsStore';

    // Reactive state from stores
    $: uiFilters = $filtersStore; // For UI display (immediate updates)
    $: queryFilters = $queryFiltersStore; // For actual queries (debounced)

    // Create the query reactively based on debounced filters
    $: clientsQuery = createClientsQuery(queryFilters);

    $: isFetching = $clientsQuery.isFetching; // True during any fetch (including background)
    $: isPending = $clientsQuery.isPending; // True when no data exists and query is loading
    $: error = $clientsQuery.error?.message || null;
    $: clients = $clientsQuery.data || [];

    // Only show loading skeleton when there's truly no data available
    $: isInitialLoading = clients.length === 0 && $clientsQuery.isPending;

    // Event handlers
    function handleSearchChange(value: string) {
        clientsActions.setSearchQuery(value);
    }

    function handleSearchClear() {
        clientsActions.setSearchQuery('');
    }

    function handleRefresh() {
        $clientsQuery.refetch();
    }

    function handleSortChange(sortBy: ClientsFilters['sortBy'], sortOrder: ClientsFilters['sortOrder']) {
        clientsActions.setSorting(sortBy, sortOrder);
    }

    function handleInviteClient() {
        // TODO: Implement invite client functionality
        toast.info('Invite client functionality coming soon!');
    }
</script>

<div class="space-y-6">
    <!-- Search and Toolbar -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <ClientsSearchBar
                bind:value={uiFilters.searchQuery}
                onValueChange={handleSearchChange}
                onClear={handleSearchClear}
        />

        <ClientsToolbar
                isLoading={isFetching}
                clientsCount={clients.length}
                filters={uiFilters}
                onRefresh={handleRefresh}
                onSortChange={handleSortChange}
                onAddClient={handleInviteClient}
        />
    </div>

    <!-- Client Cards Grid -->
    <div class="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-6 relative">
        {#if isInitialLoading}
            <!-- Show loading state only when no cached data exists -->
            <ClientsLoadingState />
        {:else if error}
            <!-- Show error state -->
            <ClientsLoadingState />
            <div class="absolute inset-0 flex items-center justify-center z-20">
                <ErrorState
                    error={error}
                    onRetry={handleRefresh}
                />
            </div>
        {:else if clients.length === 0}
            <!-- Show empty state with loading skeleton as background if fetching -->
            <ClientsLoadingState />
            <div class="absolute inset-0 flex items-center justify-center z-20">
                {#if uiFilters.searchQuery}
                    <SearchEmptyState onClear={handleSearchClear} />
                {:else}
                    <ClientsEmptyState onInvite={handleInviteClient} />
                {/if}
            </div>
        {:else}
            <!-- Show data -->
            <ClientsGrid {clients} />
        {/if}
    </div>
</div>
