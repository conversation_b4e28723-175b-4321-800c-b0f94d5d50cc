<script lang="ts">
    import type {Organization} from '$lib/types/db.types';
    import {MapPin, ArrowRight} from '@lucide/svelte';
    // @ts-ignore
    import * as Button from '$lib/components/ui/button';
    import {goto} from '$app/navigation';
    // @ts-ignore
    import * as Avatar from '$lib/components/ui/avatar';
    import {Badge} from '$lib/components/ui/badge';

    interface Props {
        client: Organization;
    }

    let {client}: Props = $props();

    /**
     * Generate initials from organization name
     */
    function getOrganizationInitials(name: string): string {
        return name
            .split(' ')
            .map(word => word[0])
            .join('')
            .toUpperCase()
            .slice(0, 2);
    }

    /**
     * Format organization location (city, country) for display
     */
    function formatOrganizationLocation(organization: Organization): string {
        const parts = [organization.city, organization.country].filter(Boolean);
        return parts.join(', ');
    }

    /**
     * Check if organization is recently created (within last 7 days)
     */
    function isRecentlyCreated(organization: Organization): boolean {
        const createdDate = new Date(organization.created_at);
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);

        return createdDate > weekAgo;
    }

    let initials = $derived(getOrganizationInitials(client.name));
    let location = $derived(formatOrganizationLocation(client));
    let isNew = $derived(isRecentlyCreated(client));

    function handleClientClick() {
        goto(`/clients/${client.id}/dashboard`);
    }
</script>

<Button.Root variant="outline" class="group h-auto p-0" onclick={handleClientClick}>
    <div class="p-4 w-full flex flex-col text-left space-y-3">
        <div class="flex items-start gap-3">
            <Avatar.Root class="size-11 rounded-lg flex-shrink-0 border">
                <Avatar.Fallback class="rounded-lg bg-muted">
                    {initials}
                </Avatar.Fallback>
            </Avatar.Root>

            <div class="flex-1 min-w-0 space-y-1">
                <h3 class="text-base truncate leading-tight" title={client.name}>
                    {client.name}
                </h3>

                <div class="flex items-center text-muted-foreground text-sm">
                    <MapPin class="w-3 h-3 mr-1 flex-shrink-0"/>
                    <span class="truncate" title={location}>
                        {location}
                    </span>
                </div>
            </div>
        </div>

        <div class="h-6 flex items-center justify-between text-muted-foreground group-hover:text-primary transition-colors">
            <div class="flex items-center">
                <span class="text-sm">Manage</span>
                <ArrowRight class="ml-1 size-3 group-hover:translate-x-0.5 transition-transform"/>
            </div>
            {#if isNew}
                <Badge class="bg-green-200 text-green-700"> New</Badge>
            {/if}
        </div>
    </div>
</Button.Root>
