import {writable, derived} from 'svelte/store';
import {createQuery} from '@tanstack/svelte-query';
import {supabase} from '$lib/supabaseClient';
import type {Organization} from '$lib/types/db.types';
import {toast} from 'svelte-sonner';
import {type BaseEntity, type BaseFilters, getErrorMessage} from './base/BaseStore';

interface ClientEntity extends Organization, BaseEntity {}

export interface ClientsFilters extends BaseFilters {
    searchQuery: string;
    sortBy: 'name' | 'city' | 'country' | 'created_at';
    sortOrder: 'asc' | 'desc';
}

const initialFilters: ClientsFilters = {
    searchQuery: '',
    sortBy: 'name',
    sortOrder: 'asc',
};

// UI search value (immediate updates for input field)
const searchInputStore = writable<string>('');

// Debounced search value (used for actual queries)
const debouncedSearchStore = writable<string>('');

// Other filters store
const otherFiltersStore = writable<{
    sortBy: ClientsFilters['sortBy'];
    sortOrder: ClientsFilters['sortOrder'];
}>({
    sortBy: 'name',
    sortOrder: 'asc',
});

// Combined filters for queries (debounced search + other filters)
const queryFiltersStore = derived(
    [debouncedSearchStore, otherFiltersStore],
    ([searchQuery, otherFilters]) => ({
        searchQuery,
        ...otherFilters,
    })
);

// UI filters for components (immediate search + other filters)
const uiFiltersStore = derived(
    [searchInputStore, otherFiltersStore],
    ([searchQuery, otherFilters]) => ({
        searchQuery,
        ...otherFilters,
    })
);

// Debounce logic
let debounceTimer: ReturnType<typeof setTimeout>;
searchInputStore.subscribe((value) => {
    clearTimeout(debounceTimer);
    debounceTimer = setTimeout(() => {
        debouncedSearchStore.set(value);
    }, 300); // 300ms debounce
});

async function fetchClients(filters: ClientsFilters): Promise<ClientEntity[]> {
    try {
        let query = supabase
            .from('organization')
            .select('*')
            .eq('type', 'tenant');

        // Add search filter using Supabase's text search
        if (filters.searchQuery.trim()) {
            const searchTerm = filters.searchQuery.trim();
            query = query.or(`name.ilike.%${searchTerm}%,city.ilike.%${searchTerm}%,country.ilike.%${searchTerm}%,line1.ilike.%${searchTerm}%,line2.ilike.%${searchTerm}%,postal_code.ilike.%${searchTerm}%,state.ilike.%${searchTerm}%`);
        }

        // Add sorting
        query = query.order(filters.sortBy, { ascending: filters.sortOrder === 'asc' });

        const {data, error} = await query;
        if (error) throw error;
        return (data || []) as ClientEntity[];
    } catch (error) {
        toast.error(getErrorMessage(error));
        throw error;
    }
}

export function createClientsQuery(queryFilters: ClientsFilters) {
    return createQuery({
        queryKey: ['clients', queryFilters],
        queryFn: () => fetchClients(queryFilters),
    });
}

export const clientsActions = {
    setSearchQuery(query: string): void {
        searchInputStore.set(query);
    },

    setSorting(sortBy: string, sortOrder: 'asc' | 'desc'): void {
        otherFiltersStore.update(filters => ({
            ...filters,
            sortBy: sortBy as ClientsFilters['sortBy'],
            sortOrder,
        }));
    },

    clearFilters(): void {
        searchInputStore.set('');
        otherFiltersStore.set({
            sortBy: 'name',
            sortOrder: 'asc',
        });
    },

    reset(): void {
        searchInputStore.set('');
        otherFiltersStore.set({
            sortBy: 'name',
            sortOrder: 'asc',
        });
    },
};

export {uiFiltersStore as filtersStore, queryFiltersStore};
