/**
 * Base interface for all entities in the application
 */
export interface BaseEntity {
    id: string;
    created_at: string;
    updated_at: string;
}

/**
 * Common filter interface that can be extended by specific stores
 */
export interface BaseFilters {
    searchQuery: string;
    sortBy: string;
    sortOrder: 'asc' | 'desc';
}



/**
 * Simple error handling utility
 */
export function getErrorMessage(error: unknown): string {
    if (error && typeof error === 'object' && 'message' in error) {
        return (error as any).message || 'An unexpected error occurred';
    }
    if (typeof error === 'string') {
        return error;
    }
    return 'An unexpected error occurred';
}
