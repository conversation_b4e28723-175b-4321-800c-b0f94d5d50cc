<script lang="ts">
    import {QueryClient, QueryClientProvider} from '@tanstack/svelte-query';
    import {browser} from '$app/environment';

    interface Props {
        children: any;
    }

    let {children}: Props = $props();

    const queryClient = new QueryClient({
        defaultOptions: {
            queries: {
                enabled: browser,
                staleTime: 5 * 60 * 1000, // 5 minutes
                gcTime: 30 * 60 * 1000, // 30 minutes
                refetchOnWindowFocus: false,
                retry: (failureCount, error) => {
                    // Don't retry client errors (4xx)
                    if (error && 'status' in error && typeof error.status === 'number') {
                        if (error.status >= 400 && error.status < 500) {
                            return false;
                        }
                    }
                    return failureCount < 3;
                },
            },
        },
    });
</script>

<QueryClientProvider client={queryClient}>
    {@render children()}
</QueryClientProvider>
