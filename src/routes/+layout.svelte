<script lang="ts">
    import '../app.css';
    import {Toaster} from '$lib/components/ui/sonner';
    import {LoaderCircle} from '@lucide/svelte';
    import {themeColor} from '$lib/stores/themeStore';
    import QueryProvider from '$lib/stores/base/QueryProvider.svelte';
    import type { LayoutData } from './$types';
    import type {Snippet} from 'svelte';

    interface Props {
        data: LayoutData;
        children: Snippet;
    }

    let {data, children}: Props = $props();
    let {redirectComplete = false} = data;
</script>

<svelte:head>
    <meta name="theme-color" content="#ffffff" media="(max-width: 767px)">
    <meta name="theme-color" content={$themeColor}/>

    {@html `
        <style>
            :root {
                --body-background: ${$themeColor} !important;
            }
        </style>
    `}
</svelte:head>

<style>
    /* Body background should always be white on mobile */
    @media (max-width: 767px) {
        :root {
            --body-background: var(--supernova) !important;
        }
    }
</style>

<QueryProvider>
    {#if !redirectComplete}
        <div class="fixed inset-0 flex items-center justify-center bg-white/90 backdrop-blur-sm z-50">
            <div class="flex flex-col items-center space-y-4">
                <LoaderCircle class="animate-spin size-10"/>
            </div>
        </div>
    {:else}
        {@render children()}
    {/if}
</QueryProvider>

<Toaster
    toastOptions={{
        classes: {
            error: "group-[.toaster]:bg-red-50 group-[.toaster]:text-red-900 group-[.toaster]:border-red-200 group-[.toaster]:shadow-lg",
            success: "group-[.toaster]:bg-green-50 group-[.toaster]:text-green-900 group-[.toaster]:border-green-200 group-[.toaster]:shadow-lg",
            warning: "group-[.toaster]:bg-yellow-50 group-[.toaster]:text-yellow-900 group-[.toaster]:border-yellow-200 group-[.toaster]:shadow-lg",
            info: "group-[.toaster]:bg-blue-50 group-[.toaster]:text-blue-900 group-[.toaster]:border-blue-200 group-[.toaster]:shadow-lg",
        },
    }}
/>
